import React, { useState, useEffect } from "react";

// Updated Net Worth Tracker with black theme & modern UI aesthetics
// Tailwind classes updated for dark mode & vibrant accent colors

const DEFAULT_YEARS = [2025, 2026, 2027];
const MONTHS = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];

function makeEmptyCategory(name = "New Item") {
  return {
    id: Math.random().toString(36).slice(2, 9),
    name,
    monthly: Array(12).fill(0),
    type: "asset",
  };
}

function initialState() {
  const years = DEFAULT_YEARS.reduce((acc, y) => {
    acc[y] = {
      categories: [
        { ...makeEmptyCategory("Cash"), type: "asset" },
        { ...makeEmptyCategory("Investments"), type: "asset" },
        { ...makeEmptyCategory("Retirement"), type: "asset" },
        { ...makeEmptyCategory("Credit Cards"), type: "liability" },
        { ...makeEmptyCategory("Mortgage"), type: "liability" },
      ],
    };
    return acc;
  }, {});
  return { years, selectedYear: DEFAULT_YEARS[0], modifiedAt: new Date().toISOString() };
}

export default function NetWorthApp() {
  const [state, setState] = useState(() => {
    try {
      const raw = localStorage.getItem("networth_v1");
      if (raw) return JSON.parse(raw);
    } catch {}
    return initialState();
  });

  const { years, selectedYear } = state;

  useEffect(() => {
    try {
      localStorage.setItem("networth_v1", JSON.stringify(state));
    } catch {}
  }, [state]);

  function updateCategory(year, catId, patch) {
    setState((s) => {
      const y = { ...s.years[year] };
      y.categories = y.categories.map((c) => (c.id === catId ? { ...c, ...patch } : c));
      return { ...s, years: { ...s.years, [year]: y }, modifiedAt: new Date().toISOString() };
    });
  }

  function updateMonthValue(year, catId, monthIndex, value) {
    const num = Number(value) || 0;
    setState((s) => {
      const y = { ...s.years[year] };
      y.categories = y.categories.map((c) => {
        if (c.id !== catId) return c;
        const monthly = [...c.monthly];
        monthly[monthIndex] = num;
        return { ...c, monthly };
      });
      return { ...s, years: { ...s.years, [year]: y }, modifiedAt: new Date().toISOString() };
    });
  }

  function computeTotalsForYear(year) {
    const y = years[year];
    const assets = Array(12).fill(0);
    const liabilities = Array(12).fill(0);
    y.categories.forEach((c) => {
      c.monthly.forEach((val, idx) => {
        if (c.type === "asset") assets[idx] += Number(val) || 0;
        else liabilities[idx] += Number(val) || 0;
      });
    });
    const net = assets.map((a, i) => a - liabilities[i]);
    return { assets, liabilities, net };
  }

  return (
    <div className="p-6 min-h-screen bg-gray-900 text-gray-100">
      <header className="mb-6">
        <h1 className="text-3xl font-bold text-indigo-400">Net Worth Tracker</h1>
        <p className="text-sm text-gray-400 mt-1">Modern dark theme UI</p>
      </header>

      <main className="bg-gray-800 p-4 rounded-xl shadow-lg">
        {!selectedYear ? (
          <div>No year selected.</div>
        ) : (
          <table className="w-full table-auto border-collapse">
            <thead>
              <tr className="text-indigo-300 border-b border-gray-700">
                <th>Category</th>
                <th>Type</th>
                {MONTHS.map((m) => (
                  <th key={m} className="text-right">{m}</th>
                ))}
                <th>Total</th>
              </tr>
            </thead>
            <tbody>
              {years[selectedYear].categories.map((c) => (
                <tr key={c.id} className="border-b border-gray-700">
                  <td>
                    <input className="bg-gray-700 rounded px-2" value={c.name} onChange={(e) => updateCategory(selectedYear, c.id, { name: e.target.value })} />
                  </td>
                  <td>
                    <select className="bg-gray-700 rounded" value={c.type} onChange={(e) => updateCategory(selectedYear, c.id, { type: e.target.value })}>
                      <option value="asset">Asset</option>
                      <option value="liability">Liability</option>
                    </select>
                  </td>
                  {c.monthly.map((val, idx) => (
                    <td key={idx} className="text-right">
                      <input className="w-20 text-right bg-gray-700 rounded px-2" value={val} onChange={(e) => updateMonthValue(selectedYear, c.id, idx, e.target.value)} />
                    </td>
                  ))}
                  <td className="text-right font-semibold text-indigo-400">{c.monthly.reduce((a, b) => a + Number(b || 0), 0)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </main>
    </div>
  );
}
